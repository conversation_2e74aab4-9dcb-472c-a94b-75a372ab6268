import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../constants/app_strings.dart';
import '../constants/file_type_constants.dart';
import '../theme/app_colors.dart';

class FileInfoDisplay extends StatelessWidget {
  final PlatformFile file;
  final bool isUploading;
  final double uploadProgress;
  final VoidCallback? onClose;

  const FileInfoDisplay({
    super.key,
    required this.file,
    required this.isUploading,
    required this.uploadProgress,
    this.onClose,
  });

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(0)} kB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _getFileSizeProgress() {
    // Show current uploaded size based on progress
    final uploadedBytes = (file.size * uploadProgress).round();
    // Ensure uploaded bytes never exceed file size
    final actualUploadedBytes = uploadedBytes > file.size ? file.size : uploadedBytes;
    return '${_formatFileSize(actualUploadedBytes)} of ${_formatFileSize(file.size)}';
  }



  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.uploadAreaBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.dashedBorder.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          // File info row
          Row(
            children: [
              // File Icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: FileTypeConstants.getFileColor(file.extension).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  FileTypeConstants.getFileIcon(file.extension),
                  color: FileTypeConstants.getFileColor(file.extension),
                  size: 24,
                ),
              ),

              const SizedBox(width: 12),

              // File details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // File name row with close button
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            file.name,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // Close button in top right
                        if (onClose != null)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: IconButton(
                              onPressed: onClose,
                              icon: const Icon(Icons.close),
                              iconSize: 18,
                              color: AppColors.secondaryText,
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // File size and upload status row
                    Row(
                      children: [
                        Text(
                          _getFileSizeProgress(),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        if (isUploading) ...[
                          const SizedBox(width: 8),
                          Text(
                            '• ${AppStrings.uploading}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          const SizedBox(width: 8),
                          const SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(
                              strokeWidth: 1.5,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryBlue,
                              ),
                            ),
                          ),
                        ],
                        // Success icon when upload is complete
                        if (!isUploading && uploadProgress >= 1.0) ...[
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.check_circle,
                            color: AppColors.successColor,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // Progress bar (only show when uploading)
          if (isUploading) ...[
            const SizedBox(height: 12),
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: uploadProgress,
                backgroundColor: AppColors.progressBarBackground,
                valueColor: const AlwaysStoppedAnimation<Color>(
                  AppColors.primaryBlue,
                ),
                minHeight: 6,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
