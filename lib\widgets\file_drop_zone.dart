import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import '../constants/app_strings.dart';
import '../theme/app_colors.dart';

class FileDropZone extends StatelessWidget {
  final VoidCallback onPickFile;
  final bool hasSelectedFile;

  const FileDropZone({
    super.key,
    required this.onPickFile,
    this.hasSelectedFile = false,
  });

  @override
  Widget build(BuildContext context) {
    final isWeb = MediaQuery.of(context).size.width > 600;
    final dropZonePadding = isWeb ? 32.0 : 20.0;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.uploadAreaBackground,
        borderRadius: BorderRadius.circular(18),
      ),
      child: DottedBorder(
        options: RoundedRectDottedBorderOptions(
          radius: const Radius.circular(18),
          color: AppColors.dashedBorder,
          strokeWidth: 2,
          dashPattern: const [8, 4],
          padding: EdgeInsets.all(dropZonePadding),
        ),
        child: <PERSON><PERSON><PERSON><PERSON>(
          width: double.infinity,
          child: Column(
          children: [
            // Browse File Button
            ElevatedButton.icon(
              onPressed: onPickFile,
              icon: Icon(Icons.upload_file, size: isWeb ? 20 : 18),
              label: Text(hasSelectedFile ? AppStrings.chooseAnotherFile : AppStrings.browseFile),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.cardBackground,
                foregroundColor: AppColors.primaryText,
                elevation: 1,
                side: const BorderSide(color: AppColors.dashedBorder),
                padding: EdgeInsets.symmetric(
                  horizontal: isWeb ? 20 : 16,
                  vertical: isWeb ? 12 : 10,
                ),
                textStyle: TextStyle(
                  fontSize: isWeb ? 16 : 14,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),

            SizedBox(height: isWeb ? 16 : 12),

            // Instructions
            Text(
              hasSelectedFile
                ? AppStrings.chooseDifferentFileOrDragDrop
                : AppStrings.chooseFileOrDragDrop,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: isWeb ? 16 : 14,
              ),
              textAlign: TextAlign.center,
            ),

           

            // File format info
            Text(
              AppStrings.pdfFormatInfo,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: isWeb ? 14 : 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          ),
        ),
      ),
    );
  }
}
