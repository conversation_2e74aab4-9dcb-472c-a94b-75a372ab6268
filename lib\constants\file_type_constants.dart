import 'package:flutter/material.dart';

class FileTypeConstants {
  // File type icons
  static const Map<String, IconData> fileIcons = {
    // Document files
    'pdf': Icons.picture_as_pdf,
    'doc': Icons.description,
    'docx': Icons.description,
    'xls': Icons.table_chart,
    'xlsx': Icons.table_chart,
    'ppt': Icons.slideshow,
    'pptx': Icons.slideshow,
    'txt': Icons.text_snippet,
    
    // Image files
    'jpg': Icons.image,
    'jpeg': Icons.image,
    'png': Icons.image,
    'gif': Icons.image,
    'bmp': Icons.image,
    'svg': Icons.image,
    'webp': Icons.image,
    
    // Video files
    'mp4': Icons.video_file,
    'avi': Icons.video_file,
    'mov': Icons.video_file,
    'wmv': Icons.video_file,
    'mkv': Icons.video_file,
    'flv': Icons.video_file,
    
    // Audio files
    'mp3': Icons.audio_file,
    'wav': Icons.audio_file,
    'flac': Icons.audio_file,
    'aac': Icons.audio_file,
    'ogg': Icons.audio_file,
    
    // Archive files
    'zip': Icons.folder_zip,
    'rar': Icons.folder_zip,
    '7z': Icons.folder_zip,
    'tar': Icons.folder_zip,
    'gz': Icons.folder_zip,
  };

  // File type colors
  static const Map<String, Color> fileColors = {
    // Document files
    'pdf': Color(0xFFE74C3C), // Red
    'doc': Color(0xFF2B579A), // Word blue
    'docx': Color(0xFF2B579A), // Word blue
    'xls': Color(0xFF217346), // Excel green
    'xlsx': Color(0xFF217346), // Excel green
    'ppt': Color(0xFFD24726), // PowerPoint orange
    'pptx': Color(0xFFD24726), // PowerPoint orange
    'txt': Color(0xFF6C757D), // Text grey
    
    // Image files
    'jpg': Color(0xFF28A745), // Image green
    'jpeg': Color(0xFF28A745), // Image green
    'png': Color(0xFF28A745), // Image green
    'gif': Color(0xFF28A745), // Image green
    'bmp': Color(0xFF28A745), // Image green
    'svg': Color(0xFF28A745), // Image green
    'webp': Color(0xFF28A745), // Image green
    
    // Video files
    'mp4': Color(0xFF6F42C1), // Video purple
    'avi': Color(0xFF6F42C1), // Video purple
    'mov': Color(0xFF6F42C1), // Video purple
    'wmv': Color(0xFF6F42C1), // Video purple
    'mkv': Color(0xFF6F42C1), // Video purple
    'flv': Color(0xFF6F42C1), // Video purple
    
    // Audio files
    'mp3': Color(0xFFE83E8C), // Audio pink
    'wav': Color(0xFFE83E8C), // Audio pink
    'flac': Color(0xFFE83E8C), // Audio pink
    'aac': Color(0xFFE83E8C), // Audio pink
    'ogg': Color(0xFFE83E8C), // Audio pink
    
    // Archive files
    'zip': Color(0xFFFD7E14), // Archive orange
    'rar': Color(0xFFFD7E14), // Archive orange
    '7z': Color(0xFFFD7E14), // Archive orange
    'tar': Color(0xFFFD7E14), // Archive orange
    'gz': Color(0xFFFD7E14), // Archive orange
  };

  // Default values
  static const IconData defaultIcon = Icons.insert_drive_file;
  static const Color defaultColor = Color(0xFF6C757D); // Default grey

  // Helper methods
  static IconData getFileIcon(String? extension) {
    if (extension == null) return defaultIcon;
    return fileIcons[extension.toLowerCase()] ?? defaultIcon;
  }

  static Color getFileColor(String? extension) {
    if (extension == null) return defaultColor;
    return fileColors[extension.toLowerCase()] ?? defaultColor;
  }
}
