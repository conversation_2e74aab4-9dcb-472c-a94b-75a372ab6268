import 'package:flutter/material.dart';

// Table configuration constants
class TableConfig {
  static const double defaultColumnWidth = 500.0;
  static const double sortIconSize = 16.0;
  static const double userIconSize = 30.0;
  static const double userIconImageSize = 30.0;
  static const double searchIconSize = 8;
  static const String sortIconPath = 'assets/icons/column_sort.png';
  static const String userIconPath = 'assets/icons/agent_round.png';
  static const String eyeIconPath = 'assets/icons/eye.png';
  static const String filterIconPath = 'assets/icons/filter.png';
  static const String userIconTablePath = 'assets/icons/user.png';
  static const String searchIconPath = 'assets/icons/search.png';
  static const double columnSpacing = 16.0;
  static const double horizontalMargin = 24.0;
  static const double dataRowMinHeight = 48.0;
  static const double dataRowMaxHeight = 52.0;
  static const int defaultItemsPerPage = 10;
  static const Color userIconBackgroundColor = Color(0xFFEEEEEE);
  static const Color tableHeaderColor = Color(0xFF7C7E89);

  // Container configuration
  static const double webContainerWidthPercentage = 0.85; // 85% of screen width
  static const double webContainerHeightPercentage = 0.75; // 75% of screen height
  static const double minContainerWidth = 1200.0;
  static const double maxContainerWidth = 1600.0;
  static const double minContainerHeight = 500.0;
  static const String fontFamily = 'Poppins';
  static const double titleFontSize = 22.0;
  static const FontWeight titleFontWeight = FontWeight.w600;
  static const double containerPadding = 34.0;
  static const double borderRadius = 12.0;
  static const double headerSpacing = 30.0;
}

// Table container title configuration
class TableContainerTitle {
  final String text;
  final IconData? icon;
  final Widget? customIcon;
  final TextStyle? style;
  final Widget? trailing;

  const TableContainerTitle({
    required this.text,
    this.icon,
    this.customIcon,
    this.style,
    this.trailing,
  });
}

// Column configuration for the table
class TableColumn {
  final String header;
  final double? width;
  final bool sortable;
  final TextAlign alignment;
  final Widget Function(dynamic value, int rowIndex, Map<String, dynamic> rowData)? customBuilder;

  const TableColumn({
    required this.header,
    this.width,
    this.sortable = false,
    this.alignment = TextAlign.left,
    this.customBuilder,
  });
}

// Action button configuration
class TableAction {
  final IconData icon;
  final String tooltip;
  final Color? color;
  final void Function(int rowIndex, Map<String, dynamic> rowData) onPressed;
  final Widget Function(int rowIndex, Map<String, dynamic> rowData)? customBuilder;

  const TableAction({
    required this.icon,
    required this.tooltip,
    required this.onPressed,
    this.color,
    this.customBuilder,
  });
}

// Filter configuration
class TableFilter {
  final String key;
  final String label;
  final List<String> options;
  final String? selectedValue;

  const TableFilter({
    required this.key,
    required this.label,
    required this.options,
    this.selectedValue,
  });
}
