import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primaryBlue = Color(0xFF4A90E2);
  static const Color primaryBlueGradientStart = Color(0xFF4A90E2);
  static const Color primaryBlueGradientEnd = Color(0xFF357ABD);
  
  // Background Colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardBackground = Colors.white;
  static const Color uploadAreaBackground = Color(0xFFF8F9FA);
  static const Color transparent = Colors.transparent;
  
  // Text Colors
  static const Color primaryText = Color(0xFF2C3E50);
  static const Color secondaryText = Color(0xFF7F8C8D);
  static const Color whiteText = Colors.white;
  
  // Border Colors
  static const Color dashedBorder = Color(0xFFBDC3C7);
  static const Color progressBarBackground = Color(0xFFE8F4FD);
  
  // Button Colors
  static const Color clearButtonColor = Color(0xFFB3D4F1);
  static const Color clearButtonTextColor = Color(0xFF2C3E50);
  
  // File Upload Colors
  static const Color pdfIconColor = Color(0xFFE74C3C);
  static const Color uploadIconColor = Color(0xFF4A90E2);
  
  // Status Colors
  static const Color successColor = Color(0xFF27AE60);
  static const Color errorColor = Color(0xFFE74C3C);
  static const Color warningColor = Color(0xFFF39C12);
  
}
