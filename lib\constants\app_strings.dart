class AppStrings {
  // Upload Document Screen
  static const String uploadClosingDocument = 'Upload Closing Document';
  static const String uploadFile = 'Upload File';
  static const String selectAndUploadFile = 'Select and upload the file';
  static const String browseFile = 'Browse File';
  static const String chooseAnotherFile = 'Choose Another File';
  static const String chooseFileOrDragDrop = 'Choose a file or drag & drop it here.';
  static const String chooseDifferentFileOrDragDrop = 'Choose a different file or drag & drop it here.';
  static const String pdfFormatInfo = 'PDF format, up to 50 MB.';
  static const String clear = 'Clear';
  static const String uploadAndContinue = 'Upload & Continue';
  static const String uploading = 'Uploading...';
  
  // File info
  static const String documentFileName = 'The document file.pdf';
  static const String fileSize = '60 kB of 120 kB';

  // Alert messages
  static const String fileSizeExceededTitle = 'File Size Exceeded';
  static const String fileSizeExceededMessage = 'The selected file exceeds the maximum allowed size of 50 MB. Please choose a smaller file.';
  static const String okButton = 'OK';
}
