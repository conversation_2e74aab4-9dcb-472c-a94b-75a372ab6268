import 'package:flutter/material.dart';
import '../constants/broker_data.dart';
import '../constants/table_constants.dart';
import '../theme/app_colors.dart';
import '../widgets/table_container.dart';
import '../widgets/broker_card_list.dart';

class BrokersScreen extends StatefulWidget {
  const BrokersScreen({super.key});

  @override
  State<BrokersScreen> createState() => _BrokersScreenState();
}

class _BrokersScreenState extends State<BrokersScreen> {
  String searchQuery = '';
  List<BrokerData> filteredBrokers = BrokerConstants.brokers;
  String _sortColumn = '';
  bool _sortAscending = true;

  void _onSearchChanged(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredBrokers = BrokerConstants.brokers;
      } else {
        filteredBrokers = BrokerConstants.brokers
            .where(
              (broker) =>
                  broker.brokerName.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  broker.emailAddress.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  broker.address.toLowerCase().contains(query.toLowerCase()),
            )
            .toList();
      }
    });
  }

  void _onBrokerAction(int rowIndex, Map<String, dynamic> rowData) {
    // Handle broker action (view details, edit, etc.)
    final brokerName = rowData['broker_name'] ?? '';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Action clicked for: $brokerName'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onSort(String columnKey, bool ascending) {
    setState(() {
      _sortColumn = columnKey;
      _sortAscending = ascending;

      filteredBrokers.sort((a, b) {
        dynamic aValue, bValue;

        switch (columnKey.toLowerCase()) {
          case 'broker name':
            aValue = a.brokerName;
            bValue = b.brokerName;
            break;
          case 'contacts':
            aValue = a.contacts;
            bValue = b.contacts;
            break;
          case 'email address':
            aValue = a.emailAddress;
            bValue = b.emailAddress;
            break;
          case 'join date':
            aValue = a.joinDate;
            bValue = b.joinDate;
            break;
          case 'address':
            aValue = a.address;
            bValue = b.address;
            break;
          case 'total agents':
            aValue = a.totalAgents;
            bValue = b.totalAgents;
            break;
          case 'total sales volume':
            aValue = a.totalSalesVolume;
            bValue = b.totalSalesVolume;
            break;
          default:
            return 0;
        }

        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return ascending ? -1 : 1;
        if (bValue == null) return ascending ? 1 : -1;

        final comparison = aValue.toString().compareTo(bValue.toString());
        return ascending ? comparison : -comparison;
      });
    });
  }

  List<Map<String, dynamic>> _convertBrokersToTableData() {
    return filteredBrokers.where((broker) =>
      broker.brokerName.isNotEmpty
    ).map((broker) {
      return {
        'broker_name': broker.brokerName,
        'contacts': broker.contacts,
        'email_address': broker.emailAddress,
        'join_date': broker.joinDate,
        'address': broker.address,
        'total_agents': broker.totalAgents,
        'total_sales_volume': broker.totalSalesVolume,
      };
    }).toList();
  }

  List<Widget> _buildFilterOptions() {
    return [
      InkWell(
        onTap: () {
          // Handle filter action
        },
        child: Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFD1D5DB)),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                TableConfig.filterIconPath,
                width: 16,
                height: 16,
                color: const Color(0xFF6B7280),
              ),
              const SizedBox(width: 6),
              const Text(
                'Filter',
                style: TextStyle(
                  color: Color(0xFF6B7280),
                  fontSize: 14,
                  fontFamily: TableConfig.fontFamily,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
      Flexible(
        child: Container(
          height: 40,
          constraints: const BoxConstraints(minWidth: 180, maxWidth: 280),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFFD1D5DB)),
          borderRadius: BorderRadius.circular(18),
        ),
        child: TextField(
          onChanged: _onSearchChanged,
          style: const TextStyle(
            fontFamily: TableConfig.fontFamily,
            fontSize: 14,
            color: Color(0xFF111827),
          ),
          decoration: InputDecoration(
            hintText: 'Search',
            hintStyle: const TextStyle(
              color: Color(0xFF6B7280),
              fontSize: 14,
              fontFamily: TableConfig.fontFamily,
              fontWeight: FontWeight.w400,
            ),
            prefixIcon: Padding(
              padding: const EdgeInsets.only(left: 6.0, top: 6.0,bottom: 6.0),
              child: Image.asset(
                TableConfig.searchIconPath,
                width: TableConfig.searchIconSize,
                height: TableConfig.searchIconSize,
                color: const Color(0xFF6B7280),
              ),
            ),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            isDense: true,
          ),
        ),
        ),
      ),
    ];
  }

  Widget _buildBrokerTable() {
    final columns = [
      TableColumn(
        header: 'Broker Name',
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final brokerName = value?.toString() ?? '';
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: TableConfig.userIconSize,
                height: TableConfig.userIconSize,
                decoration: const BoxDecoration(
                  color: TableConfig.userIconBackgroundColor,
                  shape: BoxShape.circle,
                ),
                child: ClipOval(
                  child: Image.asset(
                    TableConfig.userIconPath,
                    width: TableConfig.userIconImageSize,
                    height: TableConfig.userIconImageSize,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  brokerName,
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14,
       
        color: Color.fromARGB(255, 4, 6, 10),
        fontFamily: 'Poppins',
        height: 1.0, // 100% line height
        letterSpacing: -0.14, ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        },
      ),
      const TableColumn(
        header: 'Contacts',
        sortable: true,
      ),
      const TableColumn(
        header: 'Email Address',
        sortable: true,
      ),
      const TableColumn(
        header: 'Join Date',
        sortable: true,
      ),
      TableColumn(
        header: 'Address',
        sortable: true,
        customBuilder: (value, rowIndex, rowData) {
          final address = value?.toString() ?? '';
          return Container(
            constraints: const BoxConstraints(maxHeight: 40), // Allow for 2 lines
            child: Tooltip(
              message: address, // Show full address on hover
              child: Text(
                address,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.2,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          );
        },
      ),
      const TableColumn(
        header: 'Total Agents',
        sortable: true,
        alignment: TextAlign.left,
      ),
      const TableColumn(
        header: 'Total Sales Volume',
        sortable: true,
        alignment: TextAlign.left,
      ),
    ];

    final actions = [
      TableAction(
        icon: Icons.visibility_outlined,
        tooltip: 'View Details',
        color: AppColors.primaryBlue,
        onPressed: _onBrokerAction,
        customBuilder: (rowIndex, rowData) {
          return Container(
            width: 28,
            height: 28,
            decoration: const BoxDecoration(
              color: Color(0xFFEEEEEE),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Image.asset(
                TableConfig.eyeIconPath,
                width: 16,
                height: 16,
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    ];

    return TableContainer(
      title: TableContainerTitle(
        text: 'Brokers',
        customIcon: Image.asset(
          TableConfig.userIconTablePath,
          width: 23,
          height: 23,
          color: const Color(0xFF6B7280),
        ),
      ),
      filterOptions: _buildFilterOptions(),
      columns: columns,
      data: _convertBrokersToTableData(),
      actions: actions,
      itemsPerPage: TableConfig.defaultItemsPerPage,
      emptyMessage: 'No brokers found',
      showPagination: true,
      onSort: _onSort,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isWeb = MediaQuery.of(context).size.width > 800;

    return isWeb
        ? _buildBrokerTable()
        : Scaffold(
            backgroundColor: AppColors.backgroundColor,
            body: Padding(
              padding: const EdgeInsets.all(16),
              child: BrokerCardList(brokers: filteredBrokers),
            ),
          );
  }
}
