import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../constants/table_constants.dart';

class GenericDataTable extends StatefulWidget {
  final List<TableColumn> columns;
  final List<Map<String, dynamic>> data;
  final List<TableAction>? actions;
  final List<TableFilter>? filters;
  final int itemsPerPage;
  final String? emptyMessage;
  final bool showPagination;
  final bool showSearch;
  final String? searchHint;
  final List<String>? searchableFields;
  final void Function(String)? onSearch;
  final void Function(String, String?)? onFilter;
  final void Function(String, bool)? onSort;

  const GenericDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.actions,
    this.filters,
    this.itemsPerPage = 10,
    this.emptyMessage,
    this.showPagination = true,
    this.showSearch = false,
    this.searchHint,
    this.searchableFields,
    this.onSearch,
    this.onFilter,
    this.onSort,
  });

  @override
  State<GenericDataTable> createState() => _GenericDataTableState();
}

class _GenericDataTableState extends State<GenericDataTable> {
  int _currentPage = 1;
  String _sortColumn = '';
  bool _sortAscending = true;

  int get _totalPages => widget.showPagination 
      ? (widget.data.length / widget.itemsPerPage).ceil()
      : 1;
  
  List<Map<String, dynamic>> get _currentPageData {
    if (!widget.showPagination) {
      return widget.data.where((item) =>
        item.isNotEmpty &&
        item.values.any((value) => value != null && value.toString().trim().isNotEmpty)
      ).toList();
    }

    final startIndex = (_currentPage - 1) * widget.itemsPerPage;
    final endIndex = startIndex + widget.itemsPerPage;
    final result = widget.data.sublist(
      startIndex,
      endIndex > widget.data.length ? widget.data.length : endIndex,
    );

    // Filter out any null or empty entries
    return result.where((item) =>
      item.isNotEmpty &&
      item.values.any((value) => value != null && value.toString().trim().isNotEmpty)
    ).toList();
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= _totalPages) {
      setState(() {
        _currentPage = page;
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      _goToPage(_currentPage - 1);
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      _goToPage(_currentPage + 1);
    }
  }

  void _handleSort(String columnKey) {
    if (widget.onSort != null) {
      setState(() {
        if (_sortColumn == columnKey) {
          _sortAscending = !_sortAscending;
        } else {
          _sortColumn = columnKey;
          _sortAscending = true;
        }
      });
      widget.onSort!(columnKey, _sortAscending);
    }
  }



  Widget _buildCell(TableColumn column, dynamic value, int rowIndex, Map<String, dynamic> rowData) {
    if (column.customBuilder != null) {
      return column.customBuilder!(value, rowIndex, rowData);
    }

    final text = value?.toString() ?? '';
    final hasMultipleLines = text.contains('\n');

    // Check if this is an address column
    final isAddressColumn = column.header.toLowerCase().contains('address');

    // If the text is empty, return a minimal container to avoid empty cells
    if (text.isEmpty) {
      return Container(
        alignment: column.alignment == TextAlign.center
            ? Alignment.center
            : column.alignment == TextAlign.right
                ? Alignment.centerRight
                : Alignment.centerLeft,
        child: const Text(
          '-',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF9CA3AF), // Lighter color for empty state
            fontFamily: TableConfig.fontFamily,
          ),
        ),
      );
    }

    return Container(
      alignment: column.alignment == TextAlign.center
          ? Alignment.center
          : column.alignment == TextAlign.right
              ? Alignment.centerRight
              : Alignment.centerLeft,
      child: Tooltip(
        message: text,
        child: Text(
          text,
          overflow: TextOverflow.ellipsis,
          maxLines: isAddressColumn || hasMultipleLines ? 2 : 1,
          softWrap: true,
          textAlign: column.alignment,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500, // Medium weight (500)
            color: Color(0xFF111827),
            fontFamily: TableConfig.fontFamily,
          ),
        ),
      ),
    );
  }

  Widget _buildActionsCell(int rowIndex, Map<String, dynamic> rowData) {
    if (widget.actions == null || widget.actions!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: TableConfig.dataRowMinHeight,
      alignment: Alignment.center,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: widget.actions!.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;

          return Padding(
            padding: EdgeInsets.only(right: index < widget.actions!.length - 1 ? 8 : 0),
            child: action.customBuilder != null
                ? GestureDetector(
                    onTap: () => action.onPressed(rowIndex, rowData),
                    child: Tooltip(
                      message: action.tooltip,
                      child: Center(child: action.customBuilder!(rowIndex, rowData)),
                    ),
                  )
                : IconButton(
                    onPressed: () => action.onPressed(rowIndex, rowData),
                    icon: Icon(
                      action.icon,
                      color: action.color ?? AppColors.primaryBlue,
                      size: 20,
                    ),
                    tooltip: action.tooltip,
                  ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(48),
          child: Text(
            widget.emptyMessage ?? 'No data available',
            style: const TextStyle(
              color: AppColors.secondaryText,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        // Data table - constrained to fit within container
        Expanded(
          child: _buildDataTable(),
        ),

        // Pagination with minimal spacing
        if (widget.showPagination && _totalPages > 1) ...[
          // Minimal separator line like in reference design
          _buildPaginationSection(),
        ],
      ],
    );
  }
  Widget _buildDataTable() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < 1000;

    // Prepare columns including actions column
    final allColumns = [
      ...widget.columns,
      if (widget.actions != null && widget.actions!.isNotEmpty)
        const TableColumn(header: 'Actions', sortable: false),
    ];

    // Calculate minimum table width based on columns
    final minTableWidth = _calculateMinTableWidth(allColumns, isTabletOrSmaller);

    Widget dataTable = DataTable(
      columnSpacing: isTabletOrSmaller ? 12 : TableConfig.columnSpacing,
      horizontalMargin: 0,
      dataRowMinHeight: TableConfig.dataRowMinHeight,
      dataRowMaxHeight: TableConfig.dataRowMaxHeight,
      headingRowHeight: 56,
      dataTextStyle: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: Color(0xFF111827),
        fontFamily: TableConfig.fontFamily,
      ),
      dividerThickness: 1,
      border: TableBorder(
        bottom: BorderSide(
          color: const Color(0xFFE5E7EB),
          width: 1,
        ),
        horizontalInside: BorderSide(
          color: const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      sortColumnIndex: null,
      sortAscending: _sortAscending,
      columns: allColumns.map((column) {
        final isActionsColumn = column.header == 'Actions';
        final isCurrentSortColumn = column.header == _sortColumn;

        return DataColumn(
          label: Container(
            constraints: BoxConstraints(
              minWidth: _getColumnMinWidth(column, isTabletOrSmaller),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: isTabletOrSmaller ? 4.0 : 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      column.header,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: TableConfig.tableHeaderColor,
                        fontFamily: TableConfig.fontFamily,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (!isActionsColumn && column.sortable) ...[
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _handleSort(column.header),
                      child: Image.asset(
                        TableConfig.sortIconPath,
                        width: TableConfig.sortIconSize,
                        height: TableConfig.sortIconSize,
                        color: isCurrentSortColumn
                            ? AppColors.primaryBlue
                            : const Color(0xFF9CA3AF),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      }).toList(),
      rows: _currentPageData.asMap().entries.where((entry) {
        final rowData = entry.value;
        if (rowData.isEmpty) return false;

        final nonActionValues = rowData.entries
            .where((e) => e.key != 'actions')
            .map((e) => e.value);

        return nonActionValues.any((value) =>
          value != null &&
          value.toString().trim().isNotEmpty
        );
      }).map((entry) {
        final rowIndex = entry.key;
        final rowData = entry.value;

        return DataRow(
          cells: allColumns.map((column) {
            if (column.header == 'Actions') {
              return DataCell(
                Container(
                  width: _getColumnMinWidth(column, isTabletOrSmaller),
                  height: TableConfig.dataRowMinHeight,
                  alignment: Alignment.center,
                  child: Center(
                    child: _buildActionsCell(rowIndex, rowData),
                  ),
                ),
              );
            }

            final columnKey = column.header.toLowerCase().replaceAll(' ', '_');
            final value = rowData[columnKey];

            return DataCell(
              Container(
                constraints: BoxConstraints(
                  minWidth: _getColumnMinWidth(column, isTabletOrSmaller),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: isTabletOrSmaller ? 4.0 : 8.0),
                  child: _buildCell(column, value, rowIndex, rowData),
                ),
              ),
            );
          }).toList(),
        );
      }).toList(),
    );

    // Wrap table with proper scrolling behavior
    return _buildScrollableTable(dataTable, minTableWidth, isTabletOrSmaller);
  }

  double _calculateMinTableWidth(List<TableColumn> columns, bool isTabletOrSmaller) {
    double totalWidth = 0;
    for (final column in columns) {
      totalWidth += _getColumnMinWidth(column, isTabletOrSmaller);
    }
    // Add spacing between columns
    totalWidth += (columns.length - 1) * (isTabletOrSmaller ? 12 : TableConfig.columnSpacing);
    return totalWidth;
  }

  double _getColumnMinWidth(TableColumn column, bool isTabletOrSmaller) {
    // Define minimum widths for different column types - ensure adequate space for tablet scrolling
    switch (column.header.toLowerCase()) {
      case 'actions':
        return isTabletOrSmaller ? 80 : 80;
      case 'broker name':
        return isTabletOrSmaller ? 150 : 180;
      case 'contacts':
        return isTabletOrSmaller ? 120 : 140;
      case 'email address':
        return isTabletOrSmaller ? 180 : 200;
      case 'join date':
        return isTabletOrSmaller ? 100 : 120;
      case 'address':
        return isTabletOrSmaller ? 150 : 180;
      case 'total agents':
        return isTabletOrSmaller ? 100 : 100;
      case 'total sales volume':
        return isTabletOrSmaller ? 120 : 140;
      default:
        return isTabletOrSmaller ? 100 : 120;
    }
  }

  Widget _buildScrollableTable(Widget dataTable, double minTableWidth, bool isTabletOrSmaller) {
    final screenWidth = MediaQuery.of(context).size.width;
    // More accurate calculation of available width accounting for all container margins and padding
    final containerMargins = isTabletOrSmaller ? 24.0 : 48.0; // horizontal margins from TableContainer
    final containerPadding = isTabletOrSmaller ? 24.0 : 48.0; // horizontal padding from TableContainer
    final availableWidth = screenWidth - containerMargins - containerPadding;

    // For tablets, always enable horizontal scrolling to ensure columns are accessible
    if (isTabletOrSmaller || minTableWidth > availableWidth) {
      // Table needs horizontal scrolling
      return SingleChildScrollView(
        scrollDirection: Axis.vertical,
        physics: const ClampingScrollPhysics(),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          physics: const ClampingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: minTableWidth,
            ),
            child: dataTable,
          ),
        ),
      );
    } else {
      // Table fits within available width (desktop only)
      return SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: SizedBox(
          width: double.infinity,
          child: dataTable,
        ),
      );
    }
  }

  Widget _buildPaginationSection() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < 1000;
    final isMobile = screenWidth < 600;

    final double horizontalPadding = isTabletOrSmaller ? 4.0 : 6.0;
    // Tight vertical padding to match reference design - very close to table
    final double verticalPadding = isTabletOrSmaller ? 8.0 : 6.0;

    if (isMobile) {
      // Stack pagination vertically on mobile
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: verticalPadding),
        child: Column(
          children: [
            // Info text
            Text(
              'Showing ${(_currentPage - 1) * widget.itemsPerPage + 1} to ${(_currentPage * widget.itemsPerPage).clamp(1, widget.data.length)} of ${widget.data.length}',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF6B7280),
                fontFamily: TableConfig.fontFamily,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            // Pagination controls
            _buildPaginationControls(isMobile),
          ],
        ),
      );
    } else {
      // Horizontal layout for tablet and desktop
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: horizontalPadding, vertical: verticalPadding),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
              child: Text(
                'Showing data ${(_currentPage - 1) * widget.itemsPerPage + 1} to ${(_currentPage * widget.itemsPerPage).clamp(1, widget.data.length)} of ${widget.data.length} entries',
                style: TextStyle(
                  fontSize: isTabletOrSmaller ? 12 : 14,
                  color: const Color(0xFF6B7280),
                  fontFamily: TableConfig.fontFamily,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildPaginationControls(isMobile),
          ],
        ),
      );
    }
  }

  Widget _buildPaginationControls(bool isMobile) {
    final maxVisiblePages = isMobile ? 3 : 5;
    final startPage = (_currentPage - maxVisiblePages ~/ 2).clamp(1, _totalPages);
    final endPage = (startPage + maxVisiblePages - 1).clamp(1, _totalPages);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Previous button
        IconButton(
          onPressed: _currentPage > 1 ? _previousPage : null,
          icon: const Icon(Icons.chevron_left),
          iconSize: isMobile ? 18 : 20,
          constraints: BoxConstraints(
            minWidth: isMobile ? 28 : 32,
            minHeight: isMobile ? 28 : 32,
          ),
        ),

        // Page numbers
        ...List.generate(endPage - startPage + 1, (index) {
          final pageNumber = startPage + index;
          final isCurrentPage = pageNumber == _currentPage;

          return Container(
            margin: EdgeInsets.symmetric(horizontal: isMobile ? 1 : 2),
            child: TextButton(
              onPressed: () => _goToPage(pageNumber),
              style: TextButton.styleFrom(
                backgroundColor: isCurrentPage ? AppColors.primaryBlue : Colors.transparent,
                foregroundColor: isCurrentPage ? Colors.white : const Color(0xFF6B7280),
                minimumSize: Size(isMobile ? 28 : 32, isMobile ? 28 : 32),
                padding: EdgeInsets.zero,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              child: Text(
                pageNumber.toString(),
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14,
                  fontFamily: TableConfig.fontFamily,
                ),
              ),
            ),
          );
        }),

        // Next button
        IconButton(
          onPressed: _currentPage < _totalPages ? _nextPage : null,
          icon: const Icon(Icons.chevron_right),
          iconSize: isMobile ? 18 : 20,
          constraints: BoxConstraints(
            minWidth: isMobile ? 28 : 32,
            minHeight: isMobile ? 28 : 32,
          ),
        ),
      ],
    );
  }
}
