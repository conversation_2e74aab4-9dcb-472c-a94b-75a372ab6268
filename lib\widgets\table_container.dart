import 'package:flutter/material.dart';
import '../constants/table_constants.dart';
import '../theme/app_colors.dart';
import 'generic_data_table.dart';

class TableContainer extends StatelessWidget {
  final TableContainerTitle? title;
  final List<Widget>? filterOptions;
  final List<TableColumn> columns;
  final List<Map<String, dynamic>> data;
  final List<TableAction>? actions;
  final List<TableFilter>? filters;
  final int itemsPerPage;
  final String? emptyMessage;
  final bool showPagination;
  final bool showSearch;
  final String? searchHint;
  final List<String>? searchableFields;
  final void Function(String)? onSearch;
  final void Function(String, String?)? onFilter;
  final void Function(String, bool)? onSort;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final BoxDecoration? decoration;

  const TableContainer({
    super.key,
    this.title,
    this.filterOptions,
    required this.columns,
    required this.data,
    this.actions,
    this.filters,
    this.itemsPerPage = 10,
    this.emptyMessage,
    this.showPagination = true,
    this.showSearch = false,
    this.searchHint,
    this.searchableFields,
    this.onSearch,
    this.onFilter,
    this.onSort,
    this.width,
    this.height,
    this.padding,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isWeb = screenSize.width > 800;
    final isTabletOrSmaller = screenSize.width < 1000;

    // Calculate responsive container dimensions and margins
    final containerHeight = isWeb
        ? (screenSize.height * 0.78).clamp(600.0, double.infinity)
        : null;

    // Responsive margins - smaller on mobile/tablet
    final horizontalMargin = isTabletOrSmaller ? 12.0 : 24.0;
    final verticalMargin = isTabletOrSmaller ? 8.0 : 24.0;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        width: double.infinity,
        height: containerHeight,
        margin: EdgeInsets.symmetric(
          horizontal: horizontalMargin,
          vertical: verticalMargin,
        ),
        padding: padding ?? EdgeInsets.only(
          left: isTabletOrSmaller ? 12 : TableConfig.containerPadding,
          right: isTabletOrSmaller ? 12 : TableConfig.containerPadding,
          top: isTabletOrSmaller ? 12 : TableConfig.containerPadding,
        ),
        decoration: decoration ?? BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(TableConfig.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and Filter Section
            if (title != null || filterOptions != null) ...[
              _buildHeaderSection(context, isWeb),
              SizedBox(height: isTabletOrSmaller ? 8 : 16),
            ],

            // Table Content - simple approach
            Expanded(
              child: GenericDataTable(
                columns: columns,
                data: data,
                actions: actions,
                filters: filters,
                itemsPerPage: itemsPerPage,
                emptyMessage: emptyMessage,
                showPagination: showPagination,
                showSearch: showSearch,
                searchHint: searchHint,
                searchableFields: searchableFields,
                onSearch: onSearch,
                onFilter: onFilter,
                onSort: onSort,
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildHeaderSection(BuildContext context, bool isWeb) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < 1000; // Better breakpoint for tablet and smaller

    return Column(
        children: [
          // For tablet and smaller screens, stack title and filters vertically
          if (isTabletOrSmaller) ...[
            // Title Section
            if (title != null) ...[
              Align(
                alignment: Alignment.centerLeft,
                child: _buildTitle(),
              ),
              const SizedBox(height: 12),
            ],

            // Filter Options Section - aligned to the right
            if (filterOptions != null) ...[
              Align(
                alignment: Alignment.centerRight,
                child: Wrap(
                  spacing: 8,
                  runSpacing: 6,
                  children: filterOptions!,
                ),
              ),
            ],
          ] else ...[
            // For wide screens, keep horizontal layout
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Title Section
                if (title != null)
                  Expanded(
                    flex: 2,
                    child: _buildTitle(),
                  ),

                // Filter Options Section
                if (filterOptions != null)
                  Flexible(
                    flex: 3,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: filterOptions!.map((filter) {
                        final index = filterOptions!.indexOf(filter);
                        return Padding(
                          padding: EdgeInsets.only(
                            left: index > 0 ? 12 : 0,
                          ),
                          child: filter,
                        );
                      }).toList(),
                    ),
                  ),
              ],
            ),
          ],
        ],
    );
  }

  Widget _buildTitle() {
    final defaultStyle = TextStyle(
      fontSize: TableConfig.titleFontSize,
      fontWeight: TableConfig.titleFontWeight,
      fontFamily: TableConfig.fontFamily,
      color: AppColors.primaryText,
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (title!.customIcon != null) ...[
          title!.customIcon!,
          const SizedBox(width: 12),
        ] else if (title!.icon != null) ...[
          Icon(
            title!.icon,
            size: 24,
            color: AppColors.primaryText,
          ),
          const SizedBox(width: 12),
        ],
        Text(
          title!.text,
          style: title!.style ?? defaultStyle,
        ),
        if (title!.trailing != null) ...[
          const SizedBox(width: 12),
          title!.trailing!,
        ],
      ],
    );
  }
}

// Helper widget for creating filter buttons
class FilterButton extends StatelessWidget {
  final String label;
  final IconData? icon;
  final VoidCallback? onPressed;
  final bool isSelected;

  const FilterButton({
    super.key,
    required this.label,
    this.icon,
    this.onPressed,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? AppColors.primaryBlue : const Color(0xFFE9ECEF),
        ),
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? AppColors.primaryBlue.withValues(alpha: 0.1) : Colors.white,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: 16,
                    color: isSelected ? AppColors.primaryBlue : const Color(0xFF6C757D),
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  label,
                  style: TextStyle(
                    color: isSelected ? AppColors.primaryBlue : const Color(0xFF6C757D),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    fontFamily: TableConfig.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Helper widget for creating search fields
class SearchField extends StatelessWidget {
  final String? hintText;
  final double? width;
  final void Function(String)? onChanged;

  const SearchField({
    super.key,
    this.hintText,
    this.width,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTabletOrSmaller = screenWidth < 1000;
    final isMobile = screenWidth < 600;

    // Responsive width calculation
    final fieldWidth = width ?? (
      isMobile ? screenWidth * 0.8 : // 80% of screen width on mobile
      isTabletOrSmaller ? 200 : 300
    );

    return Container(
      width: fieldWidth,
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE9ECEF),
          width: 1,
        ),
      ),
      child: TextField(
        onChanged: onChanged,
        style: const TextStyle(
          fontFamily: TableConfig.fontFamily,
          fontSize: 14,
        ),
        decoration: InputDecoration(
          hintText: hintText ?? 'Search',
          hintStyle: const TextStyle(
            color: Color(0xFF6C757D),
            fontSize: 14,
            fontFamily: TableConfig.fontFamily,
          ),
          prefixIcon: const Icon(
            Icons.search,
            size: 18,
            color: Color(0xFF6C757D),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: isTabletOrSmaller ? 12 : 16,
            vertical: 10,
          ),
        ),
      ),
    );
  }
}
