import 'package:flutter/material.dart';
import '../constants/app_strings.dart';
import '../theme/app_colors.dart';

class UploadActions extends StatelessWidget {
  final bool hasFile;
  final bool isUploading;
  final VoidCallback onClear;
  final VoidCallback onUploadAndContinue;

  const UploadActions({
    super.key,
    required this.hasFile,
    required this.isUploading,
    required this.onClear,
    required this.onUploadAndContinue,
  });

  @override
  Widget build(BuildContext context) {
    final isWeb = MediaQuery.of(context).size.width > 600;
    final buttonPadding = isWeb
        ? const EdgeInsets.symmetric(horizontal: 35, vertical: 12)
        : const EdgeInsets.symmetric(horizontal: 35, vertical: 10);
    final fontSize = isWeb ? 16.0 : 14.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Clear button
        ElevatedButton(
          onPressed: hasFile && !isUploading ? onClear : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.clearButtonColor,
            foregroundColor: AppColors.clearButtonTextColor,
            elevation: 0,
            padding: buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            textStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
            ),
          ),
          child: const Text(AppStrings.clear),
        ),

        const SizedBox(width: 12),

        // Upload & Continue button
        ElevatedButton(
          onPressed: hasFile && !isUploading ? onUploadAndContinue : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
            foregroundColor: AppColors.whiteText,
            elevation: 0,
            padding: buttonPadding,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(25),
            ),
            textStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
            ),
          ),
          child: Text(
            isUploading ? AppStrings.uploading : AppStrings.uploadAndContinue,
          ),
        ),
      ],
    );
  }
}
