import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:dotted_border/dotted_border.dart';
import '../constants/app_strings.dart';
import '../theme/app_colors.dart';
import '../widgets/file_upload_card.dart';

class UploadDocumentScreen extends StatefulWidget {
  const UploadDocumentScreen({super.key});

  @override
  State<UploadDocumentScreen> createState() => _UploadDocumentScreenState();
}

class _UploadDocumentScreenState extends State<UploadDocumentScreen> {
  PlatformFile? selectedFile;
  bool isUploading = false;
  double uploadProgress = 0.0;

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Check file size (50MB = 50 * 1024 * 1024 bytes)
        const maxFileSize = 50 * 1024 * 1024;

        if (file.size > maxFileSize) {
          _showFileSizeExceededAlert();
          return;
        }

        setState(() {
          selectedFile = file;
        });
        _simulateUpload();
      }
    } catch (e) {
      _showErrorSnackBar('Error picking file: $e');
    }
  }

  void _simulateUpload() {
    setState(() {
      isUploading = true;
      uploadProgress = 0.0;
    });

    // Simulate upload progress
    Future.delayed(const Duration(milliseconds: 100), () {
      _updateProgress();
    });
  }

  void _updateProgress() {
    if (uploadProgress < 1.0) {
      setState(() {
        uploadProgress += 0.1;
      });
      Future.delayed(const Duration(milliseconds: 200), () {
        _updateProgress();
      });
    } else {
      setState(() {
        isUploading = false;
      });
    }
  }

  void _clearFile() {
    setState(() {
      selectedFile = null;
      isUploading = false;
      uploadProgress = 0.0;
    });
  }

  void _uploadAndContinue() {
    if (selectedFile != null && !isUploading) {
      // Handle upload and continue logic here
      _showSuccessSnackBar('File uploaded successfully!');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.errorColor,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.successColor,
      ),
    );
  }

  void _showFileSizeExceededAlert() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('File Size Exceeded'),
          content: const Text(
            'The selected file exceeds the maximum allowed size of 50 MB. Please choose a smaller file.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          margin: const EdgeInsets.all(16),
          child: FileUploadCard(
            selectedFile: selectedFile,
            isUploading: isUploading,
            uploadProgress: uploadProgress,
            onPickFile: _pickFile,
            onClearFile: _clearFile,
            onUploadAndContinue: _uploadAndContinue,
          ),
        ),
      ),
    );
  }
}
